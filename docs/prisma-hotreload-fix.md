# Prisma 热重载连接崩溃修复方案

## 问题描述

在 Next.js 开发环境中，热重载功能会导致模块重新加载，即使使用了单例模式，Prisma Client 实例仍然会被重复创建，最终导致数据库连接池耗尽和连接崩溃。

## 解决方案

### 1. 主要修复 - 使用全局变量防止多实例

**修改文件**: `packages/model/mongodb/prisma.ts`

主要改进：
- 在开发环境中使用全局变量存储 Prisma 实例
- 添加日志配置以便调试
- 提供优雅关闭方法

### 2. Admin Platform 专用客户端

**新增文件**: `admin_platform/lib/prisma.ts`

为 admin_platform 创建专门的 Prisma 客户端，确保：
- 遵循 Next.js 最佳实践
- 防止热重载时的多实例问题
- 保持与现有 API 的兼容性

### 3. 优雅关闭机制

**新增文件**: 
- `packages/model/mongodb/cleanup.ts`
- `admin_platform/lib/cleanup.ts`

提供进程退出时的数据库连接清理功能。

## 使用方法

### 在应用启动时设置优雅关闭

```typescript
// 在应用入口文件中添加
import { setupGracefulShutdown } from './packages/model/mongodb/cleanup'

// 设置优雅关闭监听器
setupGracefulShutdown()
```

### Admin Platform 中的使用

所有 admin_platform 中的文件现在都使用本地的 Prisma 客户端：

```typescript
// 旧的导入方式
import { PrismaMongoClient } from '../../../../packages/model/mongodb/prisma'

// 新的导入方式
import { PrismaMongoClient } from '@/lib/prisma'
```

## 技术细节

### 全局变量机制

```typescript
const globalForPrisma = global as unknown as {
  prismaInstance: PrismaClient | undefined
  prismaConfigInstance: PrismaClient | undefined
}
```

在开发环境中，Prisma 实例被存储在全局对象上，确保热重载时不会创建新实例。

### 环境区分

- **开发环境**: 使用全局变量，启用详细日志
- **生产环境**: 使用静态变量，仅记录错误日志

### 连接池配置

Prisma Client 现在包含适当的日志配置：
- 开发环境: `['error', 'warn']`
- 生产环境: `['error']`

## 验证修复效果

1. 启动 admin_platform 开发服务器
2. 修改代码触发热重载
3. 观察数据库连接数是否稳定
4. 检查控制台是否有连接相关错误

## 注意事项

1. 确保在生产环境中正确设置环境变量 `NODE_ENV=production`
2. 如果仍然遇到连接问题，可以检查数据库的最大连接数设置
3. 在容器化部署时，确保优雅关闭机制正常工作

## 后续优化建议

1. 考虑使用连接池监控工具
2. 添加数据库连接健康检查
3. 实现连接重试机制
4. 考虑使用 Prisma 的连接池配置选项
