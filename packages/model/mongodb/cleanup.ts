import { PrismaMongoClient } from './prisma'
import logger from '../logger/logger'

/**
 * 优雅关闭数据库连接
 * 在应用程序退出时调用此函数以确保所有数据库连接被正确关闭
 */
export async function gracefulShutdown(): Promise<void> {
  try {
    logger.trace('正在关闭数据库连接...')
    await PrismaMongoClient.disconnect()
    logger.trace('数据库连接已关闭')
  } catch (error) {
    logger.error('关闭数据库连接时出错:', error)
  }
}

/**
 * 设置进程退出监听器
 * 自动在进程退出时清理数据库连接
 */
export function setupGracefulShutdown(): void {
  // 监听进程退出信号
  const signals = ['SIGINT', 'SIGTERM', 'SIGQUIT'] as const

  signals.forEach((signal) => {
    process.on(signal, async () => {
      logger.trace(`收到 ${signal} 信号，开始优雅关闭...`)
      await gracefulShutdown()
      process.exit(0)
    })
  })

  // // 监听未捕获的异常
  // process.on('uncaughtException', async (error) => {
  //   logger.error('未捕获的异常:', error)
  //   await gracefulShutdown()
  //   process.exit(1)
  // })
  //
  // // 监听未处理的 Promise 拒绝
  // process.on('unhandledRejection', async (reason, promise) => {
  //   logger.error('未处理的 Promise 拒绝:', reason, 'at:', promise)
  //   await gracefulShutdown()
  //   process.exit(1)
  // })
}
