'use server'
import { AccountTeacherData } from '@/app/type/account'
import { JuZiWecomClient } from '../../../../packages/lib/juzi/client'
import { PrismaMongoClient } from '@/lib/prisma'


export async function getTeacherByName(imBotId: string, userName: string):Promise<AccountTeacherData[]> {

  let currentPage = 0
  const res: AccountTeacherData[] = []
  const set = new Set<string>()

  while (true) {
    currentPage += 1
    const url = 'v2/customer/list'
    const client = new JuZiWecomClient()
    const param = {
      current: currentPage,
      pageSize: 1000,
      imBotId
    }
    const response = await client.get(url, param)

    const datas = (response.data as any).data as (any[]|undefined)
    if (!datas || datas.length === 0) break

    for (const data of datas) {
      if (data.name.includes(userName) && !set.has(data.imContactId)) {
        set.add(data.imContactId)
        res.push({
          imContactId: data.imContactId,
          name: data.name,
          wecomUserId: data.imInfo.externalUserId
        })
      }
    }
  }

  return res
}

export async function updateGroupTeacher(accountWechatId: string, userData: AccountTeacherData):Promise<void> {
  await PrismaMongoClient.getInstance().group_teacher.upsert({
    where: {
      accountWechatId:accountWechatId
    },
    update: {
      imContactId:  userData.imContactId,
      name: userData.name,
      wecomUserId: userData.wecomUserId
    },
    create:{
      accountWechatId:accountWechatId,
      imContactId:  userData.imContactId,
      name: userData.name,
      wecomUserId: userData.wecomUserId ?? ''
    }
  })
}